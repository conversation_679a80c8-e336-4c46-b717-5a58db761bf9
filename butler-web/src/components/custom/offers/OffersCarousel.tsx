"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { getActiveOffersForCustomers } from "@/server/marketing";
import { Offer } from "@/app/type";
import {
  Gift,
  Percent,
  Tag,
  Clock,
  Users,
  ShoppingCart,
  Sparkles,
  ChevronLeft,
  ChevronRight,
  Calendar,
  Star,
} from "lucide-react";

interface OffersCarouselProps {
  outletId?: string;
  foodChainId?: string;
  className?: string;
  onOfferClick?: (offer: Offer) => void;
}

export default function OffersCarousel({
  outletId,
  foodChainId,
  className = "",
  onOfferClick,
}: OffersCarouselProps) {
  const [offers, setOffers] = useState<Offer[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    fetchActiveOffers();
  }, [outletId, foodChainId]);

  const fetchActiveOffers = async () => {
    try {
      setLoading(true);
      console.log("🔍 Fetching offers with params:", { outletId, foodChainId });

      const response = await getActiveOffersForCustomers({
        outletId,
        foodChainId,
      });

      console.log("📦 Offers API response:", response);

      if (response.success) {
        // Filter offers that should be displayed on app
        const displayableOffers = (response.data || []).filter(
          (offer: Offer) => offer.displayOnApp !== false
        );
        console.log("✨ Displayable offers:", displayableOffers);
        setOffers(displayableOffers);
      } else {
        console.error("❌ Failed to fetch offers:", response.message);
      }
    } catch (error) {
      console.error("💥 Error fetching active offers:", error);
    } finally {
      setLoading(false);
    }
  };

  const getOfferIcon = (offerType: string) => {
    switch (offerType) {
      case "BOGO":
        return <Gift className="h-4 w-4" />;
      case "discount":
      case "minimumAmount":
      case "quantityDiscount":
        return <Percent className="h-4 w-4" />;
      case "freeItem":
        return <Gift className="h-4 w-4" />;
      case "combo":
        return <ShoppingCart className="h-4 w-4" />;
      case "customerTier":
        return <Users className="h-4 w-4" />;
      case "firstTime":
        return <Sparkles className="h-4 w-4" />;
      case "timeBasedSpecial":
      case "dayOfWeek":
        return <Clock className="h-4 w-4" />;
      case "dateRange":
        return <Calendar className="h-4 w-4" />;
      default:
        return <Tag className="h-4 w-4" />;
    }
  };

  const getOfferGradient = (offerType: string) => {
    const gradients: { [key: string]: string } = {
      BOGO: "from-green-400 to-green-600",
      combo: "from-blue-400 to-blue-600",
      discount: "from-purple-400 to-purple-600",
      freeItem: "from-orange-400 to-orange-600",
      quantityDiscount: "from-indigo-400 to-indigo-600",
      multiDishType: "from-pink-400 to-pink-600",
      minimumAmount: "from-yellow-400 to-yellow-600",
      dayOfWeek: "from-cyan-400 to-cyan-600",
      dateRange: "from-red-400 to-red-600",
      customerTier: "from-emerald-400 to-emerald-600",
      firstTime: "from-violet-400 to-violet-600",
      timeBasedSpecial: "from-amber-400 to-amber-600",
    };
    return gradients[offerType] || "from-gray-400 to-gray-600";
  };

  const getOfferTitle = (offer: Offer) => {
    const { offerType, discountDetails } = offer;

    switch (offerType) {
      case "BOGO":
        return `Buy ${discountDetails.buyQuantity} Get ${discountDetails.getQuantity} FREE`;
      case "discount":
        return discountDetails.discountType === "percentage"
          ? `${discountDetails.discountValue}% OFF`
          : `₹${discountDetails.discountValue} OFF`;
      case "minimumAmount":
        return discountDetails.discountType === "percentage"
          ? `${discountDetails.discountValue}% OFF`
          : `₹${discountDetails.discountValue} OFF`;
      case "quantityDiscount":
        return `${
          discountDetails.discountType === "percentage"
            ? `${discountDetails.discountValue}% OFF`
            : `₹${discountDetails.discountValue} OFF`
        } on ${discountDetails.buyQuantity}+ items`;
      case "freeItem":
        return "FREE ITEM";
      case "combo":
        return `COMBO @ ₹${discountDetails.comboPrice}`;
      case "firstTime":
        return "FIRST TIME SPECIAL";
      default:
        return offer.name;
    }
  };

  const getOfferSubtitle = (offer: Offer) => {
    const { offerType, discountDetails } = offer;

    switch (offerType) {
      case "minimumAmount":
        return `On orders above ₹${discountDetails.minimumOrderValue}`;
      case "freeItem":
        return discountDetails.freeItemId?.name || "Special item";
      case "customerTier":
        return "Exclusive for VIP customers";
      case "firstTime":
        return "Welcome offer for new customers";
      case "timeBasedSpecial":
        return "Limited time offer";
      case "dayOfWeek":
        return "Special day offer";
      default:
        return offer.description || "";
    }
  };

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % offers.length);
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + offers.length) % offers.length);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
  };

  if (loading || offers.length === 0) {
    return null; // Don't show anything if loading or no offers
  }

  return (
    <div className={`relative ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <Star className="h-5 w-5 text-yellow-500" />
          Special Offers
        </h3>
        {offers.length > 1 && (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={prevSlide}
              className="h-8 w-8 p-0"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={nextSlide}
              className="h-8 w-8 p-0"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      <div className="relative overflow-hidden">
        <div
          className="flex transition-transform duration-300 ease-in-out"
          style={{ transform: `translateX(-${currentIndex * 100}%)` }}
        >
          {offers.map((offer, index) => (
            <div key={offer._id || index} className="w-full flex-shrink-0">
              <Card
                className={`bg-gradient-to-r ${getOfferGradient(
                  offer.offerType
                )} text-white cursor-pointer hover:shadow-lg transition-shadow`}
                onClick={() => onOfferClick?.(offer)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        {getOfferIcon(offer.offerType)}
                        <Badge
                          variant="secondary"
                          className="text-xs bg-white/20 text-white border-white/30"
                        >
                          {offer.offerType === "BOGO"
                            ? "BOGO"
                            : offer.offerType === "freeItem"
                            ? "FREE"
                            : offer.offerType === "combo"
                            ? "COMBO"
                            : offer.offerType === "firstTime"
                            ? "NEW"
                            : "OFFER"}
                        </Badge>
                      </div>

                      <h4 className="font-bold text-lg mb-1">
                        {getOfferTitle(offer)}
                      </h4>

                      <p className="text-sm text-white/90 mb-2">
                        {getOfferSubtitle(offer)}
                      </p>

                      <div className="flex items-center justify-between">
                        <div className="text-xs text-white/80">
                          Valid till {formatDate(offer.endDate.toString())}
                        </div>
                        {offer.autoApply && (
                          <Badge
                            variant="secondary"
                            className="text-xs bg-white/20 text-white border-white/30"
                          >
                            Auto Apply
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>

      {offers.length > 1 && (
        <div className="flex justify-center mt-3 gap-2">
          {offers.map((_, index) => (
            <button
              key={index}
              className={`w-2 h-2 rounded-full transition-colors ${
                index === currentIndex ? "bg-gray-800" : "bg-gray-300"
              }`}
              onClick={() => setCurrentIndex(index)}
            />
          ))}
        </div>
      )}
    </div>
  );
}
