"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Offer } from "@/app/type";
import { 
  TrendingUp, 
  Users, 
  DollarSign, 
  Target,
  Calendar,
  Percent,
  Gift,
  BarChart3
} from "lucide-react";

interface OfferAnalyticsProps {
  offer: Offer;
  className?: string;
}

export default function OfferAnalytics({ offer, className = "" }: OfferAnalyticsProps) {
  const analytics = offer.analytics || {};
  const usageRules = offer.usageRules || {};
  
  // Calculate usage percentage
  const usagePercentage = usageRules.usageLimit > 0 
    ? Math.min((usageRules.usedCount || 0) / usageRules.usageLimit * 100, 100)
    : 0;
  
  // Calculate days remaining
  const endDate = new Date(offer.endDate);
  const now = new Date();
  const daysRemaining = Math.max(0, Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));
  
  // Calculate average savings per use
  const avgSavingsPerUse = (analytics.totalUsage || 0) > 0 
    ? (analytics.totalSavings || 0) / (analytics.totalUsage || 1)
    : 0;

  const getUsageStatus = () => {
    if (usageRules.usageLimit === 0) return "unlimited";
    if (usagePercentage >= 100) return "exhausted";
    if (usagePercentage >= 80) return "high";
    if (usagePercentage >= 50) return "medium";
    return "low";
  };

  const getUsageStatusColor = () => {
    const status = getUsageStatus();
    switch (status) {
      case "exhausted": return "text-red-600 bg-red-50";
      case "high": return "text-orange-600 bg-orange-50";
      case "medium": return "text-yellow-600 bg-yellow-50";
      case "low": return "text-green-600 bg-green-50";
      default: return "text-blue-600 bg-blue-50";
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Usage</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.totalUsage || 0}
                </p>
              </div>
              <div className="p-2 bg-blue-50 rounded-lg">
                <BarChart3 className="h-5 w-5 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Savings</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(analytics.totalSavings || 0)}
                </p>
              </div>
              <div className="p-2 bg-green-50 rounded-lg">
                <DollarSign className="h-5 w-5 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Unique Customers</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.uniqueCustomers?.length || 0}
                </p>
              </div>
              <div className="p-2 bg-purple-50 rounded-lg">
                <Users className="h-5 w-5 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg. Savings</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(avgSavingsPerUse)}
                </p>
              </div>
              <div className="p-2 bg-orange-50 rounded-lg">
                <TrendingUp className="h-5 w-5 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Usage Progress and Status */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Target className="h-5 w-5" />
              Usage Progress
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {usageRules.usageLimit > 0 ? (
              <>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">
                    {usageRules.usedCount || 0} / {usageRules.usageLimit} uses
                  </span>
                  <Badge className={getUsageStatusColor()}>
                    {Math.round(usagePercentage)}%
                  </Badge>
                </div>
                <Progress value={usagePercentage} className="h-2" />
                <p className="text-xs text-gray-500">
                  {usageRules.usageLimit - (usageRules.usedCount || 0)} uses remaining
                </p>
              </>
            ) : (
              <div className="text-center py-4">
                <Badge className="bg-blue-50 text-blue-600">
                  Unlimited Usage
                </Badge>
                <p className="text-sm text-gray-500 mt-2">
                  This offer has no usage limit
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Time Status
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Days Remaining</span>
              <Badge variant={daysRemaining > 7 ? "default" : daysRemaining > 0 ? "destructive" : "secondary"}>
                {daysRemaining > 0 ? `${daysRemaining} days` : "Expired"}
              </Badge>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Start Date:</span>
                <span>{new Date(offer.startDate).toLocaleDateString()}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">End Date:</span>
                <span>{new Date(offer.endDate).toLocaleDateString()}</span>
              </div>
            </div>

            {offer.autoApply && (
              <div className="mt-4 p-3 bg-green-50 rounded-lg">
                <div className="flex items-center gap-2">
                  <Gift className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium text-green-800">
                    Auto-Applied Offer
                  </span>
                </div>
                <p className="text-xs text-green-600 mt-1">
                  This offer is automatically applied to eligible orders
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Percent className="h-5 w-5" />
            Performance Metrics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {usageRules.perCustomerLimit > 0 
                  ? `${usageRules.perCustomerLimit}x`
                  : "∞"
                }
              </div>
              <p className="text-sm text-gray-600">Per Customer Limit</p>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {usageRules.perOrderLimit || 1}x
              </div>
              <p className="text-sm text-gray-600">Per Order Limit</p>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {offer.stackingRules?.priority || 0}
              </div>
              <p className="text-sm text-gray-600">Priority Level</p>
            </div>
          </div>

          {offer.stackingRules?.canStackWithOthers && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-2">
                <Target className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-800">
                  Stackable Offer
                </span>
              </div>
              <p className="text-xs text-blue-600 mt-1">
                This offer can be combined with other compatible offers
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
