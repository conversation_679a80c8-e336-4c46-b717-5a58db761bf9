/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import React, { useRef, useState, useEffect, Suspense } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import Image from "next/image";
import "./chat.css";
import { useTheme } from "@/contexts/ThemeContext";
import { useAuth } from "@/contexts/AuthContext";
import { Input } from "@/components/ui/input";
import { Conversation, Dish } from "@/app/type";
import { formatTime } from "@/app/helper/time";
import { Button } from "@/components/ui/button";
import { Icon } from "@iconify/react/dist/iconify.js";
import {
  ChevronLeft,
  Minus,
  Plus,
  ShoppingBag,
  ShoppingCart,
  MapPin,
  Trash2,
  LogOut,
  Clock,
} from "lucide-react";
import {
  botName,
  formatStringToHtml,
  generateChatPlaceHolder,
  generateInitialMessages,
} from "@/app/helper/chat";
import {
  clearConversation,
  conversation,
  getConversation,
  getOutletMenu,
} from "@/server/user";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import useCart from "@/hooks/useCart";
import { arrayOperations, stringReducer } from "@/app/helper/general";
import OffersCarousel from "@/components/custom/offers/OffersCarousel";
import { Offer } from "@/app/type";
import { calculateTotalWithOffers, CartItem } from "@/utils/offerCalculations";
// import MicRecorder from "@/components/custom/users/MicRecorder";
import TextToSpeech from "@/components/custom/users/TextToSpeech";
import { useLanguagePreference } from "../../../../hooks/useLanguagePreference";
import LanguageSelector from "../../../../components/LanguageSelector";

const Page = () => {
  const { cart, setCart } = useCart();
  const router = useRouter();
  const {
    language,
    setLanguage,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    isLoading: languageLoading,
  } = useLanguagePreference();
  const params = useSearchParams();
  const outletId = params?.get("outletId") || "";
  const [isCartMenuOpen, setIsCartMenuOpen] = useState(false);
  const foodChainId = params?.get("chainId") || "";
  const [isOpen, setIsOpen] = useState(false);
  const [userMessage, setUserMessage] = useState("");
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [isMenuClicked, setIsMenuClicked] = useState(false);
  const { theme, isLoading, setChainAndOutlet } = useTheme();
  const { logout } = useAuth();
  const [responseLoading, setResponseLoading] = useState(false);
  const [placeHolder, setPlaceholder] = useState(generateChatPlaceHolder());
  const [conversationHistory, setConversationHistory] = useState<
    Conversation[]
  >([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [menu, setMenu] = useState<{ category: string; dishes: Dish[] }[]>([]);

  // Reset animation after some time of inactivity
  useEffect(() => {
    let timer: any;
    if (isMenuClicked) {
      timer = setTimeout(() => {
        setIsMenuClicked(false);
      }, 60000); // Reset after 1 minute of inactivity
    }
    return () => clearTimeout(timer);
  }, [isMenuClicked]);

  // Handle category selection
  const handleCategorySelect = (category: any) => {
    setSelectedCategory(category);
  };

  const handleAddToCart = (item: Dish) => {
    arrayOperations("append-unique", cart, setCart, item);
  };

  const handleUpdateQuantity = (
    dish: Dish,
    action: "increase" | "decrease"
  ) => {
    setCart((prevCart) => {
      return prevCart
        .map((item) => {
          if (item._id === dish._id) {
            const currentQuantity = item.quantity || 1;
            if (action === "decrease" && currentQuantity === 1) {
              // Remove item if quantity would become 0
              return null;
            }
            return {
              ...item,
              quantity:
                action === "increase"
                  ? currentQuantity + 1
                  : currentQuantity - 1,
            };
          }
          return item;
        })
        .filter(Boolean) as Dish[]; // Remove null items
    });
  };

  useEffect(() => {
    if (foodChainId) {
      setChainAndOutlet(foodChainId);
    }
  }, [foodChainId]);

  const getOutletMenuFunc = async () => {
    if (!foodChainId || !outletId) return;
    const response = await getOutletMenu(String(foodChainId), String(outletId));
    const set = new Set(response.data.map((item: any) => item.category.name));

    // Add categories message
    setConversationHistory([
      {
        sender: botName.lowerCase,
        message: generateInitialMessages(),
        time: new Date().getTime(),
      },
      {
        sender: "user",
        message: "Can you please show me the menu?",
        time: new Date().getTime(),
      },
      {
        sender: botName.lowerCase,
        message: `We are serving the following categories: ${[...set].join(
          ", "
        )}`,
        time: new Date().getTime(),
        dishes: response.data,
        suggestedQuestions: [
          response.data[0]?.name ? `What should I try in ${[...set][0]}?` : "",
          response.data[0]?.name
            ? `Add ${response.data[0]?.name} to my order`
            : "",
        ],
      },
    ]);

    // Use saved recommended dishes if available, otherwise use all dishes
    const dishesToShow = response.data;
    updateConversationWithGroupedDishes(dishesToShow, [
      response.data[0]?.name ? `What should I try in ${[...set][0]}?` : "",
      response.data[0]?.name ? `Add ${response.data[0]?.name} to my order` : "",
    ]);

    setMenu(groupDishesByCategory(response.data) as any);

    // Get previous conversations
    const conversationData = await getConversation(String(outletId));
    if (conversationData?.data?.messages) {
      setConversationHistory((prev) => [
        ...prev,
        ...conversationData.data.messages,
      ]);
    }
  };

  useEffect(() => {
    // Then fetch menu and previous conversations
    setTimeout(() => {
      getOutletMenuFunc();
    }, 100);
  }, []);

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      const scrollContainer =
        chatContainerRef.current.querySelector(".overflow-y-scroll");
      if (scrollContainer) {
        setTimeout(() => {
          scrollContainer.scrollTop = scrollContainer.scrollHeight;
        }, 100);
      }
    }
  };

  const handleClearConversation = () => {
    clearConversation(String(outletId)).then(() => {
      router.push(`/conversations`);
    });
    setConversationHistory([]);
  };

  useEffect(() => {
    scrollToBottom();
  }, [conversationHistory, responseLoading]);

  const handleNewMessage = (message: string) => {
    if (responseLoading) return;

    setResponseLoading(true);

    // Add user's message to conversation history
    setConversationHistory((prev) => [
      ...prev,
      { sender: "user", message, time: new Date().getTime() },
    ]);

    let fullText = "";

    // Stream data from the backend
    const days = 1;
    const dayAgo = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

    const recentConversation = conversationHistory
      .slice(-6) // Get the last 6 entries
      .filter((item) => item?.time && new Date(item?.time) >= dayAgo);

    conversation(
      String(foodChainId),
      userMessage || message,
      (text: string) => {
        fullText += text;
        setConversationHistory((prev) => {
          // Update the butler's latest message or create a new one
          const lastMessage = prev[prev.length - 1];
          if (lastMessage?.sender === botName.lowerCase) {
            return [
              ...prev.slice(0, -1),
              { ...lastMessage, message: fullText },
            ];
          }
          return [
            ...prev,
            {
              sender: botName.lowerCase,
              message: fullText,
              time: new Date().getTime(),
            },
          ];
        });
      },
      (dishes: Dish[], faqSuggestions: string[]) => {
        // Always update conversation, even with empty dishes
        if (faqSuggestions?.length > 0) {
          setPlaceholder(faqSuggestions[0]);
        }
        // Pass dishes (even if empty) to the update function
        updateConversationWithGroupedDishes(dishes || [], faqSuggestions || []);
      },
      () => {
        setResponseLoading(false);
        setUserMessage(""); // Clear the user message input
      },
      String(outletId),
      recentConversation || [],
      language // Pass the selected language
    );
  };

  // Function to group dishes by category
  const groupDishesByCategory = (dishes: Dish[]) => {
    // Create an object to hold grouped dishes
    let isFeatured = false;
    const groupedDishes: any = {
      Featured: [],
    };

    // Group dishes by their category
    dishes.forEach((dish: any) => {
      const category = dish?.category?.name || "-";
      if (!groupedDishes[category]) {
        groupedDishes[category] = [];
      }
      groupedDishes[category].push(dish);
      if (dish?.isFeatured) {
        isFeatured = true;
        groupedDishes.Featured.push(dish);
      }
    });
    if (!isFeatured) {
      delete groupedDishes.Featured;
    }

    // Convert to array format if needed
    return Object.entries(groupedDishes).map(([category, dishes]) => ({
      category,
      dishes,
    }));
  };

  // Update the conversation history with grouped dishes
  const updateConversationWithGroupedDishes = (
    dishes: Dish[],
    faqSuggestions: string[],
    cartOperationResult?: any
  ) => {
    // Handle empty dishes array gracefully
    const dishesArray = Array.isArray(dishes) ? dishes : [];
    const groupedDishes =
      dishesArray.length > 0 ? groupDishesByCategory(dishesArray) : [];

    setConversationHistory((prev: any) => {
      const lastMessage = prev[prev.length - 1];
      if (lastMessage?.sender === botName.lowerCase) {
        return [
          ...prev.slice(0, -1),
          {
            ...lastMessage,
            groupedDishes: groupedDishes,
            dishes: dishesArray,
            suggestedQuestions: faqSuggestions || [],
            hasDishes: dishesArray.length > 0,
            cartOperation: cartOperationResult, // Add cart operation result
          },
        ];
      }
      return prev;
    });

    // If there was a cart operation, highlight the dish but don't modify the cart
    // The user will need to click the + button to actually add it
    if (cartOperationResult?.success) {
      // Instead of automatically adding/removing from cart,
      // we'll just make sure the dish is in the recommendations
      // The user can then add it manually using the + button
      // We could optionally highlight the dish in the UI to draw attention to it
      // This could be done by adding a temporary class or state
      // For now, we'll just ensure the dish is visible in the recommendations
      // The actual cart modification will be done by the user clicking the + button
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      const input = e.currentTarget;
      if (input.value.trim()) {
        handleNewMessage(input.value);
        input.value = "";
      }
    }
  };

  const handleButtonClick = () => {
    const input = document.querySelector("input");
    if (input && input.value.trim()) {
      handleNewMessage(input.value);
      input.value = "";
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div>Loading...</div>
      </div>
    );
  }

  return (
    <div
      className="min-h-screen flex flex-col"
      style={{
        backgroundColor: theme.primaryColor + "20",
      }}
    >
      <header className="sticky top-0 z-10 bg-white border-b shadow-sm">
        <div className="max-w-screen-xl mx-auto px-2 py-1 md:px-4 md:py-3 flex items-center justify-between w-full">
          <div className="flex items-center gap-1 md:gap-3">
            <Button
              variant="ghost"
              size="icon"
              className="rounded-full h-8 w-8"
              onClick={() => {
                router.push(
                  localStorage.getItem("to-outlets")
                    ? "/outlets"
                    : `/conversations`
                );
                localStorage.removeItem("to-outlets");
              }}
              aria-label="Back to conversations"
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>

            <div id="amoeba"></div>

            <h1 className="font-semibold text-lg">
              {theme?.name} {botName?.upperCase}
            </h1>
          </div>

          <div className="flex items-center gap-2">
            {/* Language Selector */}
            <LanguageSelector
              selectedLanguage={language}
              onLanguageChange={setLanguage}
              className="mr-2"
            />

            <DropdownMenu
              open={isCartMenuOpen}
              onOpenChange={(open) => {
                setIsCartMenuOpen(open);
                if (open) setIsMenuClicked(true);
              }}
            >
              <DropdownMenuTrigger asChild>
                <div className="relative">
                  <Button
                    variant={"ghost"}
                    className="rounded-full shadow-md cursor-pointer"
                    style={{ background: theme.primaryColor, color: "white" }}
                    // onClick={() => router.push("/checkout")}
                  >
                    <ShoppingCart />
                  </Button>
                  {cart.length != 0 && (
                    <div className="bg-red-500 h-6 w-6 flex justify-center text-white text-xs p-1 rounded-full absolute -top-2 -right-2">
                      {cart.length}
                    </div>
                  )}
                </div>
              </DropdownMenuTrigger>

              <DropdownMenuContent
                className="w-96 p-0 rounded-lg border-0 shadow-xl mr-6"
                sideOffset={5}
              >
                <div className="flex flex-col">
                  <div
                    className="bg-gradient-to-r p-4 rounded-t-lg"
                    style={{
                      backgroundImage: `linear-gradient(to right, ${theme.primaryColor}, ${theme.primaryColor}99)`,
                    }}
                  >
                    <DropdownMenuLabel className="text-white text-lg font-bold flex items-center gap-2">
                      <ShoppingCart size={20} />
                      Your Order ({cart.length})
                    </DropdownMenuLabel>
                    <p className="text-white text-sm opacity-90">
                      Total: ₹
                      {cart.reduce(
                        (sum, item) => sum + item.price * (item.quantity || 1),
                        0
                      )}
                    </p>
                  </div>

                  <ScrollArea className="max-h-[60vh] overflow-y-auto">
                    {cart.length === 0 ? (
                      <div className="p-8 text-center text-gray-500">
                        <ShoppingBag
                          size={40}
                          className="mx-auto mb-3 opacity-50"
                        />
                        <p>Your cart is empty</p>
                        <p className="text-sm mt-1">Add items to get started</p>
                      </div>
                    ) : (
                      cart.map((dish, dishIndex) => (
                        <div key={dishIndex} className="relative">
                          <div className="flex justify-between p-4 hover:bg-gray-50 transition-colors">
                            <div className="flex-1 pr-16">
                              <div className="flex items-center gap-2">
                                <span className="font-medium">{dish.name}</span>
                                {dish.isVeg && (
                                  <div className="h-4 w-4 border border-green-600 flex items-center justify-center">
                                    <div className="h-2 w-2 bg-green-600 rounded-full"></div>
                                  </div>
                                )}
                              </div>

                              {dish.description && (
                                <div className="text-xs text-gray-500 mt-1 line-clamp-2">
                                  {stringReducer(dish.description, 20)}
                                </div>
                              )}

                              <div className="flex items-center mt-2">
                                <Badge
                                  variant="outline"
                                  className="font-bold"
                                  style={{
                                    color: theme.primaryColor,
                                    borderColor: theme.primaryColor,
                                  }}
                                >
                                  ₹{dish.price * (dish.quantity || 1)}
                                </Badge>
                              </div>
                            </div>

                            <div className="absolute right-4 top-1/2 -translate-y-1/2 flex items-center gap-1">
                              <Button
                                onClick={() =>
                                  handleUpdateQuantity(dish, "decrease")
                                }
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 rounded-full bg-gray-100 hover:bg-gray-200"
                                style={{ color: theme.primaryColor }}
                              >
                                <Minus size={16} />
                              </Button>

                              <span className="w-6 text-center font-medium">
                                {dish.quantity || 1}
                              </span>

                              <Button
                                onClick={() =>
                                  handleUpdateQuantity(dish, "increase")
                                }
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 rounded-full bg-gray-100 hover:bg-gray-200"
                                style={{ color: theme.primaryColor }}
                              >
                                <Plus size={16} />
                              </Button>
                            </div>
                          </div>
                          {dishIndex < cart.length - 1 && (
                            <Separator className="my-0 mx-4" />
                          )}
                        </div>
                      ))
                    )}
                  </ScrollArea>

                  <div className="p-4 border-t bg-gray-50 rounded-b-lg">
                    <div className="flex justify-between mb-3 font-medium">
                      <span>Subtotal</span>
                      <span>
                        ₹
                        {cart.reduce(
                          (sum, item) =>
                            sum + item.price * (item.quantity || 1),
                          0
                        )}
                      </span>
                    </div>
                    <Button
                      className="w-full py-6 rounded-md font-medium text-white text-sm transition-colors flex items-center justify-center gap-2"
                      style={{ backgroundColor: theme.primaryColor }}
                      onClick={() =>
                        router.push(`/checkout${window.location.search}`)
                      }
                      disabled={cart.length === 0}
                    >
                      <ShoppingBag size={18} />
                      Proceed to Checkout
                    </Button>
                  </div>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 rounded-full"
                >
                  <Icon
                    icon="simple-line-icons:options-vertical"
                    width="20"
                    height="20"
                  />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-48">
                <DropdownMenuLabel>Settings</DropdownMenuLabel>
                <Separator />
                <DropdownMenuItem onClick={() => handleClearConversation()}>
                  <span className="flex items-center justify-between w-full">
                    Clear Conversation <Trash2 className="h-4 w-4" />
                  </span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    localStorage.setItem("to-chat", "true");
                    router.push("/orders");
                  }}
                >
                  <span className="flex items-center justify-between w-full">
                    Order History <Clock className="h-4 w-4" />
                  </span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => router.push("/outlets")}>
                  <span className="flex items-center justify-between w-full">
                    Find Outlets <MapPin className="h-4 w-4" />
                  </span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => logout()}>
                  <span className="flex items-center justify-between w-full">
                    Logout <LogOut className="h-4 w-4" />
                  </span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>

      <div className="px-2 flex-grow overflow-y-auto" ref={chatContainerRef}>
        <div className="bg-white border min-h-[80vh] h-[80vh] rounded-2xl p-3 flex flex-col gap-4 overflow-y-scroll">
          {conversationHistory.map((conversation, index) => {
            const isUserSender = conversation.sender === "user";
            const currentTime = conversation.time
              ? new Date(conversation.time).getTime()
              : null;
            const previousTime =
              index > 0 && conversationHistory[index - 1]?.time
                ? new Date(conversationHistory[index - 1].time!).getTime()
                : null;
            // Calculate time difference in minutes
            const timeDifference =
              currentTime && previousTime
                ? (currentTime - previousTime) / (1000 * 60)
                : 0;

            // Show separator if time difference is more than 30 minutes (you can adjust this)
            const showSeparator = index > 0 && timeDifference > 60;

            // Helper function to format the separator date
            const formatSeparatorDate = (timestamp: number) => {
              const date = new Date(timestamp);
              const today = new Date();
              const yesterday = new Date(today);
              yesterday.setDate(yesterday.getDate() - 1);

              if (date.toDateString() === today.toDateString()) {
                return "Recent";
              } else if (date.toDateString() === yesterday.toDateString()) {
                return "Yesterday";
              } else {
                return date.toLocaleDateString("en-US", {
                  weekday: "long",
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                });
              }
            };
            return (
              <div key={index}>
                {/* Time Separator */}
                {showSeparator && (
                  <div className="flex items-center justify-center my-6">
                    <div className="flex-grow h-px bg-gray-200"></div>
                    <div className="px-4 py-2 bg-gray-100 rounded-full text-xs text-gray-500 font-medium">
                      {currentTime && formatSeparatorDate(currentTime)}
                    </div>
                    <div className="flex-grow h-px bg-gray-200"></div>
                  </div>
                )}

                <div
                  className={`flex items-end fle relative ${
                    isUserSender ? "justify-end" : "justify-start"
                  } ${index == 0 ? `mt-4` : `mt-2`}`}
                >
                  <div
                    className={`relative max-w-4/5 p-3 rounded-lg shadow-md text-sm min-w-[50px] ${
                      !isUserSender
                        ? ` text-white rounded-bl-none`
                        : " text-black rounded-br-none"
                    }`}
                    style={{
                      background: isUserSender ? "#d0d0d0" : theme.primaryColor,
                    }}
                    id="streamingText"
                  >
                    <div
                      className={`text-xs text-gray-400 absolute top-[-1rem] ${
                        isUserSender ? "right-0" : "left-0"
                      }`}
                    >
                      {conversation.time
                        ? formatTime(new Date(conversation.time).getTime())
                        : ""}
                    </div>
                    <div
                      dangerouslySetInnerHTML={{
                        __html: formatStringToHtml(conversation.message),
                      }}
                    ></div>
                    {!isUserSender && (
                      <TextToSpeech
                        text={conversation.message}
                        options={{
                          voice: "Google US English",
                        }}
                      />
                    )}
                  </div>
                </div>

                {/* Display FAQ suggestions if available */}
                {!isUserSender &&
                  conversation.suggestedQuestions &&
                  conversation.suggestedQuestions.length > 0 && (
                    <div className="mt-4 mb-4 ml-4">
                      <div className="flex items-center text-sm text-gray-600 mb-2">
                        <Icon
                          icon="carbon:help"
                          className="mr-2"
                          width="16"
                          height="16"
                        />
                        <span className="font-medium">Suggested Questions</span>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {conversation.suggestedQuestions.map(
                          (question, qIndex) => (
                            <div
                              key={qIndex}
                              className="cursor-pointer px-3 py-2 bg-gray-50 border border-gray-200 rounded-full text-sm hover:bg-blue-50 hover:border-blue-200 transition-all"
                              style={{ color: theme.secondaryColor }}
                              onClick={() => {
                                setUserMessage(question);
                                handleNewMessage(question);
                              }}
                            >
                              {question}
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}
                {conversation.dishes && conversation.dishes.length > 0 && (
                  <div className="mt-4 mb-4 ml-4 w-[90%]">
                    <div className="flex items-center text-sm text-gray-600 mb-2">
                      <Icon
                        icon="carbon:restaurant"
                        className="mr-2"
                        width="16"
                        height="16"
                      />
                      <span className="font-medium">Recommended Dishes</span>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 mt-3 p-4">
                      {conversation.dishes.map((dish, dishIndex) => (
                        <div
                          key={dishIndex}
                          className={`bg-white border relative border-${
                            dish?.isFeatured ? "yellow" : "gray"
                          }-200 rounded-lg shadow-sm hover:shadow-md transition-all overflow-hidden flex flex-col`}
                        >
                          {dish.image && (
                            <div className="h-32 w-full overflow-hidden relative">
                              <Image
                                src={dish.image}
                                alt={dish.name}
                                fill
                                sizes="(max-width: 768px) 100vw, 33vw"
                                className="object-cover"
                                style={{ objectFit: "cover" }}
                              />
                            </div>
                          )}
                          <div className="p-3 flex flex-col flex-grow">
                            <div className="flex justify-between items-start mb-1">
                              <h3
                                className="font-medium text-sm flex items-center gap-1"
                                style={{ color: theme.secondaryColor }}
                              >
                                {dish?.isFeatured && (
                                  <div className="">
                                    <Icon
                                      icon="mdi:star"
                                      width="20"
                                      height="20"
                                      className="text-yellow-500"
                                    />
                                  </div>
                                )}
                                {dish.name}
                              </h3>
                              <div className="flex items-center gap-1">
                                {dish.isVeg !== undefined ? (
                                  dish.isVeg ? (
                                    <div className="h-4 w-4 border border-green-600 flex items-center justify-center flex-shrink-0 ml-1">
                                      <div className="h-2 w-2 bg-green-600 rounded-full"></div>
                                    </div>
                                  ) : (
                                    <div className="h-4 w-4 border border-red-600 flex items-center justify-center flex-shrink-0 ml-1">
                                      <div className="h-2 w-2 bg-red-600 rounded-full"></div>
                                    </div>
                                  )
                                ) : null}
                              </div>
                            </div>

                            {dish.description && (
                              <p className="text-xs text-gray-500 mb-2 line-clamp-2">
                                {dish.description}
                              </p>
                            )}

                            <div className="mt-auto flex items-center justify-between">
                              <Badge
                                variant="outline"
                                className="font-bold"
                                style={{
                                  color: theme.primaryColor,
                                  borderColor: theme.primaryColor,
                                }}
                              >
                                ₹{dish.price}
                              </Badge>

                              <Button
                                onClick={() => handleAddToCart(dish)}
                                variant="ghost"
                                size="sm"
                                className="h-8 rounded-full flex items-center justify-center transition-colors bg-gray-100 hover:bg-gray-200 ml-2"
                                style={{ color: theme.primaryColor }}
                              >
                                {cart.some((i) => i._id === dish._id) ? (
                                  <div className="flex items-center">
                                    <Minus size={14} className="mr-1" />
                                    <span className="text-xs">Remove</span>
                                  </div>
                                ) : (
                                  <div className="flex items-center">
                                    <Plus size={14} className="mr-1" />
                                    <span className="text-xs">Add</span>
                                  </div>
                                )}
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
          {responseLoading && (
            <div
              style={{
                background: theme.primaryColor + "20",
                borderLeft: `4px solid ${theme.primaryColor}`,
                boxShadow: "0 2px 5px rgba(0,0,0,0.1)",
              }}
              className="text-center p-3 rounded-xl mt-4 flex items-center justify-center"
            >
              <div className="flex items-center space-x-2">
                <div className="flex space-x-1">
                  {[0, 1, 2].map((dot) => (
                    <div
                      key={dot}
                      className="h-2 w-2 rounded-full"
                      style={{
                        background: theme.primaryColor,
                        animation: `pulse 1.5s infinite ease-in-out ${
                          dot * 0.2
                        }s`,
                      }}
                    />
                  ))}
                </div>
                <span className="font-medium">Cooking your response...</span>
              </div>
              <style jsx>{`
                @keyframes pulse {
                  0%,
                  100% {
                    transform: scale(1);
                    opacity: 1;
                  }
                  50% {
                    transform: scale(1.2);
                    opacity: 0.7;
                  }
                }
              `}</style>
            </div>
          )}
        </div>
      </div>
      <div className="p-2 flex gap-1 sm:gap-4 justify-center border-t  w-full">
        <div className="w-full sm:w-4/5 relative bg-white rounded-full">
          <Input
            className="rounded-full w-[calc(100%-50px)] focus:outline-blue-100 border-none h-16 bg-white px-4 pr-12"
            placeholder={
              conversationHistory.at(-1)?.suggestedQuestions?.[0] || placeHolder
            }
            autoFocus={userMessage.trim() === ""}
            onKeyDown={handleKeyPress}
            value={userMessage}
            onChange={(e) => setUserMessage(e.target.value)}
          />
          <div className="absolute top-4 right-2.5">
            {userMessage.trim() === "" || responseLoading ? (
              // <MicRecorder
              //   setUserMessage={(message) => {
              //     setUserMessage(message);
              //   }}
              //   userMessage={userMessage}
              // />
              <></>
            ) : (
              <div className="flex items-center gap-2">
                <div
                  className="rounded-full cursor-pointer"
                  onClick={() => setUserMessage("")}
                >
                  <Icon icon="iconoir:cancel" width="24" height="24" />
                </div>
                <Button
                  className={`rounded-full cursor-pointer`}
                  style={{ backgroundColor: theme.primaryColor }}
                  onClick={() => handleButtonClick()}
                  disabled={userMessage.trim() === "" || responseLoading}
                >
                  <Icon icon="fluent:send-32-regular" width="32" height="32" />
                </Button>
              </div>
            )}
          </div>
        </div>
        <div>
          <DropdownMenu
            open={isOpen}
            onOpenChange={(open) => {
              setIsOpen(open);
              if (open) setIsMenuClicked(true);
            }}
          >
            <DropdownMenuTrigger asChild>
              <div
                className={`relative flex justify-center items-center rounded-full h-10 w-10 cursor-pointer shadow-lg transition-all duration-800 hover:shadow-xl ${
                  !isMenuClicked ? "animate-bounce" : ""
                } ${isOpen ? "scale-110" : "scale-100"}`}
                style={{
                  backgroundColor: theme.primaryColor,
                  border: `2px solid ${theme.primaryColor}`,
                  boxShadow: `0 0 10px ${theme.primaryColor}40`,
                }}
              >
                <Icon
                  icon="ion:restaurant-outline"
                  width="24"
                  height="24"
                  className="text-white"
                />
                <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-medium">
                  {menu.reduce(
                    (acc, category) =>
                      acc +
                      (category.category === "Featured"
                        ? 0
                        : category.dishes.length),
                    0
                  )}
                </div>
              </div>
            </DropdownMenuTrigger>

            <DropdownMenuContent
              className="w-80 p-0 rounded-lg border-0 shadow-2xl mr-6"
              sideOffset={5}
            >
              <div className="flex flex-col">
                <div
                  className="bg-gradient-to-r p-4 rounded-t-lg"
                  style={{
                    backgroundImage: `linear-gradient(to right, ${theme.primaryColor}, ${theme.primaryColor}99)`,
                  }}
                >
                  <DropdownMenuLabel className="text-white text-lg font-bold">
                    Menu
                  </DropdownMenuLabel>
                  <p className="text-white text-sm opacity-90">
                    Select items to order
                  </p>
                </div>

                {/* Offers Carousel */}
                <div className="p-4 bg-gray-50">
                  <OffersCarousel
                    outletId={outletId}
                    foodChainId={foodChainId}
                    className="mb-2"
                    onOfferClick={(offer) => {
                      console.log("Offer clicked:", offer);
                      // You can add offer application logic here
                    }}
                  />
                </div>

                {/* Category Navigation */}
                <ScrollArea className="max-h-16 border-b">
                  <div className="flex space-x-2 p-2 overflow-x-auto">
                    {menu.map((category, index) => (
                      <button
                        key={index}
                        onClick={() => {
                          handleCategorySelect(
                            category.category == selectedCategory
                              ? ""
                              : category.category
                          );
                          document
                            .getElementById(category.category)
                            ?.scrollIntoView({ behavior: "smooth" });
                        }}
                        className={`px-3 py-1 rounded-full text-sm whitespace-nowrap transition-colors ${
                          selectedCategory === category.category
                            ? "bg-gray-200 font-medium"
                            : "bg-gray-100 hover:bg-gray-200"
                        }`}
                      >
                        {category.category}
                      </button>
                    ))}
                  </div>
                </ScrollArea>

                {/* Menu Items */}
                <ScrollArea className="max-h-[50vh] overflow-y-scroll">
                  {menu.map((category, categoryIndex) => (
                    <div
                      key={categoryIndex}
                      id={`category-${category.category}`}
                      className={`py-2 ${
                        selectedCategory &&
                        selectedCategory !== category.category
                          ? "opacity-60"
                          : ""
                      }`}
                    >
                      <DropdownMenuLabel
                        className="text-base font-bold px-4 py-2 flex justify-between items-center"
                        id={category.category}
                      >
                        {category.category}
                        <span className="text-xs text-gray-500">
                          {category.dishes.length} items
                        </span>
                      </DropdownMenuLabel>

                      {category.dishes.map((dish, dishIndex) => (
                        <div key={dishIndex} className="relative">
                          <div className="flex justify-between px-4 py-3 hover:bg-gray-50 cursor-pointer">
                            <div className="flex-1">
                              <div className="font-medium">
                                {dish.name} {dish?.isFeatured && "🌟"}
                              </div>
                              {dish.description && (
                                <div className="text-xs text-gray-500 mt-1 pr-8">
                                  {stringReducer(dish.description, 500)}
                                </div>
                              )}
                              <div className="flex items-center mt-2">
                                <Badge
                                  variant="outline"
                                  className="font-bold mr-2"
                                  style={{
                                    color: theme.primaryColor,
                                    borderColor: theme.primaryColor,
                                  }}
                                >
                                  ₹{dish.price}
                                </Badge>
                                {/* {dish.isVeg && (
                            <div className="h-4 w-4 border border-green-600 flex items-center justify-center">
                              <div className="h-2 w-2 bg-green-600 rounded-full"></div>
                            </div>
                          )}
                          {dish.isSpicy && (
                            <Icon icon="mdi:chili-hot" className="ml-2 text-red-500" />
                          )}
                          {dish.isBestSeller && (
                            <Badge className="ml-2 bg-yellow-500 text-xs">Bestseller</Badge>
                          )} */}
                              </div>
                            </div>
                            <Button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleAddToCart(dish);
                              }}
                              variant={"ghost"}
                              className="absolute right-4 top-1/2 cursor-pointer -translate-y-1/2 h-8 w-8 rounded-full flex items-center justify-center transition-colors bg-gray-100 hover:bg-gray-200"
                              style={{ color: theme.primaryColor }}
                            >
                              {cart.some((i) => i._id === dish._id) ? (
                                <Minus size={18} />
                              ) : (
                                <Plus size={18} />
                              )}
                            </Button>
                          </div>
                          {dishIndex < category.dishes.length - 1 && (
                            <Separator className="my-0 mx-4" />
                          )}
                        </div>
                      ))}

                      {categoryIndex < menu.length - 1 && (
                        <Separator className="my-2" />
                      )}
                    </div>
                  ))}
                </ScrollArea>

                <div className="p-3 border-t bg-gray-50 rounded-b-lg">
                  <button
                    className="w-full py-2 rounded-md font-medium text-white text-sm transition-colors"
                    style={{ backgroundColor: theme.primaryColor }}
                    onClick={() => {
                      setIsOpen(false);
                      setIsCartMenuOpen(true);
                    }}
                  >
                    View Cart
                  </button>
                </div>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
};

const ChatPage = () => {
  return (
    <Suspense>
      <Page />
    </Suspense>
  );
};

export default ChatPage;
